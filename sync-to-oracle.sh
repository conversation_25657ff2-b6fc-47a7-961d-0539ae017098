#!/bin/bash

# Sync current chat project structure to Oracle Cloud
# This script syncs both backend and frontend to match the current local structure

set -e

# Configuration
ORACLE_HOST="*************"
SSH_KEY="/Users/<USER>/Documents/augment-projects/chatko/ssh-key-2025-07-16 (3).key"
SSH_USER="ubuntu"
REMOTE_BASE_DIR="/home/<USER>"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Test SSH connection
test_ssh() {
    log "Testing SSH connection..."
    if ! ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SSH_USER@$ORACLE_HOST" "echo 'SSH OK'" >/dev/null 2>&1; then
        error "SSH connection failed. Check server status and SSH key."
        exit 1
    fi
    success "SSH connection successful"
}

# Sync backend files
sync_backend() {
    log "Syncing backend files..."
    
    # Create remote directory structure
    ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "
        mkdir -p $REMOTE_BASE_DIR/chat-server/{src,middleware,tests,logs,tmp,ssl,nginx/conf.d}
        mkdir -p $REMOTE_BASE_DIR/chat-server/tmp/tts_cache
    "
    
    # Sync backend files (from current directory since we're in chat-backend/)
    rsync -avz --delete \
        -e "ssh -i '$SSH_KEY'" \
        --exclude 'node_modules' \
        --exclude 'logs/*' \
        --exclude 'tmp/*' \
        --exclude '*.tar.gz' \
        --exclude '.env' \
        ./ "$SSH_USER@$ORACLE_HOST:$REMOTE_BASE_DIR/chat-server/"
    
    success "Backend files synced"
}

# Sync frontend files
sync_frontend() {
    log "Syncing frontend files..."
    
    # Sync agent-integration-spark (React frontend)
    ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "
        mkdir -p $REMOTE_BASE_DIR/chat-frontend/agent-integration-spark
        mkdir -p $REMOTE_BASE_DIR/chat-frontend/cloudflare-clean
    "
    
    rsync -avz --delete \
        -e "ssh -i '$SSH_KEY'" \
        --exclude 'node_modules' \
        --exclude 'dist' \
        --exclude '.env' \
        ../agent-integration-spark/ "$SSH_USER@$ORACLE_HOST:$REMOTE_BASE_DIR/chat-frontend/agent-integration-spark/"

    # Sync cloudflare-clean (simple frontend)
    rsync -avz --delete \
        -e "ssh -i '$SSH_KEY'" \
        --exclude 'node_modules' \
        ../cloudflare-clean/ "$SSH_USER@$ORACLE_HOST:$REMOTE_BASE_DIR/chat-frontend/cloudflare-clean/"
    
    success "Frontend files synced"
}

# Setup PM2 and start services
setup_services() {
    log "Setting up services on Oracle server..."
    
    ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" << 'EOF'
set -e

cd /home/<USER>/chat-server

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install --production
fi

# Stop existing PM2 processes
echo "🛑 Stopping existing PM2 processes..."
pm2 stop oracle-voice-chat 2>/dev/null || true
pm2 delete oracle-voice-chat 2>/dev/null || true

# Create logs directory
mkdir -p logs

# Start with PM2
echo "🚀 Starting with PM2..."
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Show status
echo "📊 PM2 Status:"
pm2 status

echo "✅ Services started successfully!"
EOF
    
    success "Services configured and started"
}

# Test deployment
test_deployment() {
    log "Testing deployment..."
    
    # Test health endpoint
    if curl -f -s "http://$ORACLE_HOST:3000/health" >/dev/null; then
        success "Health check passed"
    else
        warning "Health check failed - service might still be starting"
    fi
    
    # Show PM2 status
    ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "pm2 status"
}

# Main function
main() {
    echo "🚀 Syncing Chat Project to Oracle Cloud"
    echo "======================================="
    echo "Server: $ORACLE_HOST"
    echo "Backend: chat-backend/ → /home/<USER>/chat-server/"
    echo "Frontend: agent-integration-spark/ → /home/<USER>/chat-frontend/agent-integration-spark/"
    echo "Frontend: cloudflare-clean/ → /home/<USER>/chat-frontend/cloudflare-clean/"
    echo ""
    
    read -p "Continue with sync? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Sync cancelled"
        exit 0
    fi
    
    test_ssh
    sync_backend
    sync_frontend
    setup_services
    test_deployment
    
    success "🎉 Sync completed successfully!"
    echo ""
    echo "🌐 Server endpoints:"
    echo "   Backend: http://$ORACLE_HOST:3000"
    echo "   Health: http://$ORACLE_HOST:3000/health"
    echo "   WebSocket: ws://$ORACLE_HOST:3000/ws"
    echo ""
    echo "🔧 Management commands:"
    echo "   ssh -i '$SSH_KEY' $SSH_USER@$ORACLE_HOST"
    echo "   pm2 status"
    echo "   pm2 logs oracle-voice-chat"
    echo "   pm2 restart oracle-voice-chat"
}

# Run main function
main "$@"
