import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import fs from "fs";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    headers: {
      'Content-Type': 'application/javascript',
    },
  },
  build: {
    rollupOptions: {
      output: {
        format: 'es',
        entryFileNames: '[name]-[hash].js',
        chunkFileNames: '[name]-[hash].js',
        assetFileNames: '[name]-[hash].[ext]',
      },
    },
  },
  publicDir: 'public',
  assetsInclude: ['**/_headers', '**/_redirects'],
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
    // Copy _headers and _redirects to dist
    {
      name: 'copy-cloudflare-files',
      writeBundle() {
        const files = ['_headers', '_redirects'];
        files.forEach(file => {
          if (fs.existsSync(file)) {
            fs.copyFileSync(file, `dist/${file}`);
            console.log(`✅ Copied ${file} to dist/`);
          }
        });
      }
    }
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
