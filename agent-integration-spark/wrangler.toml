name = "agent-integration-spark"
compatibility_date = "2024-07-25"

# Pages configuration
pages_build_output_dir = "dist"

# Environment variables for production
[vars]
ORACLE_BACKEND = "https://*************"
WS_URL = "wss://*************"

# Environment variables for preview deployments
[env.preview.vars]
ORACLE_BACKEND = "https://*************"
WS_URL = "wss://*************"

# Custom headers for MIME types
[[headers]]
for = "*.js"
[headers.values]
"Content-Type" = "application/javascript"

[[headers]]
for = "*.mjs"
[headers.values]
"Content-Type" = "application/javascript"

[[headers]]
for = "*.ts"
[headers.values]
"Content-Type" = "application/javascript"

[[headers]]
for = "*.tsx"
[headers.values]
"Content-Type" = "application/javascript"
