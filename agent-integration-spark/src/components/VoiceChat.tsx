import React, { useState, useRef, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Phone, Mic, MicOff, Volume2, Play, Pause, Square, Zap, Clock, MessageCircle, BarChart3 } from "lucide-react";

interface VoiceChatProps {
  className?: string;
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export const VoiceChat: React.FC<VoiceChatProps> = ({ className = '' }) => {
  // State management - pôvodná logika z agent-integration-spark
  const [isRecording, setIsRecording] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [demoStatus, setDemoStatus] = useState<'idle' | 'connecting' | 'ready' | 'talking' | 'processing'>('idle');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [voiceStatus, setVoiceStatus] = useState('Click to start demo');

  // WebSocket and audio references
  const wsRef = useRef<WebSocket | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Voice Activity Detection state
  const isRunningRef = useRef(false);
  const lastSpeechTimeRef = useRef(Date.now());
  const isSpeakingRef = useRef(false);
  const silenceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentTranscriptRef = useRef('');
  const speechStartTimeRef = useRef(0);
  const isPlayingResponseRef = useRef(false);
  const isProcessingAudioRef = useRef(false);
  const waitingForTTSRef = useRef(false);
  const lastAiMessageRef = useRef('');
  const ttsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const lastTokenTimeRef = useRef(0);
  const tokenCountRef = useRef(0);
  const consecutiveSpeechCountRef = useRef(0);
  const consecutiveSilenceCountRef = useRef(0);
  const waitingForFirstTokenRef = useRef(false);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Configuration
  const API_BASE = 'https://*************';
  const WS_URL = 'wss://*************';

  // Add system message
  const addSystemMessage = (content: string) => {
    setMessages(prev => [...prev, {
      role: 'system',
      content,
      timestamp: new Date()
    }]);
  };

  // Add regular message
  const addMessage = (role: 'user' | 'assistant', content: string) => {
    setMessages(prev => [...prev, {
      role,
      content,
      timestamp: new Date()
    }]);
  };

  // Log function
  const log = (message: string) => {
    console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
  };

  // Update status
  const updateStatus = (message: string, status: typeof demoStatus) => {
    setVoiceStatus(message);
    setDemoStatus(status);
  };

  // Test connection to Oracle backend
  const testConnection = async () => {
    try {
      log('🔗 Testujem pripojenie na Oracle backend...');
      updateStatus('Testujem pripojenie...', 'connecting');

      const response = await fetch(`${API_BASE}/health`, {
        method: 'GET',
        headers: { 'Accept': 'text/plain' }
      });

      if (response.ok) {
        const text = await response.text();
        log(`✅ Oracle HTTPS backend pripojený: ${text}`);
        updateStatus('✅ Oracle HTTPS pripojený', 'connecting');
        return true;
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      log(`⚠️ Priame pripojenie zlyhalo: ${error}`);
      // Fallback - assume Oracle backend is available and try WebSocket
      log(`🔄 Skúšam WebSocket pripojenie...`);
      updateStatus('✅ Skúšam WebSocket', 'connecting');
      return true;
    }
  };

  // Start demo - pôvodná logika z agent-integration-spark
  const startDemo = async () => {
    setDemoStatus('connecting');

    const connected = await testConnection();
    if (connected) {
      setTimeout(() => {
        connectWebSocket();
      }, 1000);
    }
  };

  const toggleRecording = () => {
    if (!isConnected) return;

    if (isRecording) {
      stopVoiceChat();
    } else {
      startVoiceChat();
    }
  };



  // Connect WebSocket
  const connectWebSocket = () => {
    try {
      log('🔌 Pripájam sa na WebSocket...');
      updateStatus('Pripájam WebSocket...', 'connecting');

      wsRef.current = new WebSocket(`${WS_URL}/ws`);
      wsRef.current.binaryType = 'arraybuffer';

      wsRef.current.onopen = () => {
        log('✅ WebSocket pripojený!');
        updateStatus('✅ WebSocket pripojený', 'ready');
        setIsConnected(true);

        startPingPong();
      };

      wsRef.current.onmessage = (event) => {
        log(`📨 WebSocket správa prijatá (${typeof event.data})`);

        if (typeof event.data === 'string') {
          try {
            const data = JSON.parse(event.data);
            handleMessage(data);
          } catch (error) {
            log(`❌ Chyba pri parsovaní správy: ${error}`);
          }
        } else {
          // Handle binary audio data
          log(`📨 Binary data: ${event.data.byteLength} bajtov`);
          handleAudioData(event.data);
        }
      };

      wsRef.current.onclose = (event) => {
        log(`🔌 WebSocket zatvorený: ${event.code} - ${event.reason}`);
        updateStatus('❌ WebSocket zatvorený', 'idle');
        setIsConnected(false);
        stopPingPong();

        // Try to reconnect after 3 seconds (only for certain codes)
        if (event.code !== 1000 && event.code !== 1001) {
          setTimeout(() => {
            if (!isConnected) {
              log('🔄 Pokúšam sa o opätovné pripojenie...');
              connectWebSocket();
            }
          }, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        log(`❌ WebSocket chyba: ${error}`);
        updateStatus('❌ WebSocket chyba', 'idle');
      };

    } catch (error) {
      log(`❌ Chyba pri vytváraní WebSocket: ${error}`);
      updateStatus('❌ WebSocket chyba', 'idle');
    }
  };

  // Handle WebSocket messages
  const handleMessage = async (data: any) => {
    log(`📨 Správa: ${data.type}`);

    switch (data.type) {
      case 'connection':
        setSessionId(data.sessionId);
        if (data.features) {
          log(`🎯 Dostupné funkcie: ${data.features.join(', ')}`);
        }
        break;

      case 'transcription':
        // SPEED OPTIMIZATION: Immediate message processing without logging
        addMessage('user', data.transcript);
        updateStatus('🤖 Generujem odpoveď...', 'processing');
        break;

      case 'ai_response':
        // SPEED OPTIMIZATION: Immediate message processing
        addMessage('assistant', data.message);
        updateStatus('🔊 Generujem TTS...', 'processing');

        // Store message for TTS
        lastAiMessageRef.current = data.message;
        waitingForTTSRef.current = true;

        // SPEED OPTIMIZATION: Reduced timeout to 5 seconds for faster failure detection
        ttsTimeoutRef.current = setTimeout(() => {
          if (waitingForTTSRef.current) {
            waitingForTTSRef.current = false;
            setVoiceStatus('❌ TTS timeout - skúste znovu');
            resetDetectionStateForNewConversation().catch(() => {});
          }
        }, 5000);
        break;

      case 'voice_ended':
        log(`🔇 Voice session ukončená`);
        updateStatus('🎤 Pripravený na rozpoznávanie hlasu...', 'ready');
        break;

      case 'pong':
        log(`🏓 Pong prijatý`);
        break;

      case 'error':
        log(`❌ Chyba: ${data.message}`);
        addSystemMessage(`❌ ${data.message}`);
        updateStatus('❌ Chyba pri spracovaní', 'ready');
        // Reset detection state after error to allow new conversation
        resetDetectionStateForNewConversation().catch(err =>
          log(`❌ Chyba pri resete detection state: ${err.message}`)
        );
        break;

      default:
        log(`❓ Neznámy typ správy: ${data.type}`);
    }
  };

  // Handle binary audio data (TTS) - OPTIMIZED FOR SPEED
  const handleAudioData = (audioBuffer: ArrayBuffer) => {
    try {
      // Cancel TTS timeout immediately - we have Piper TTS data
      if (ttsTimeoutRef.current) {
        clearTimeout(ttsTimeoutRef.current);
        ttsTimeoutRef.current = null;
      }
      waitingForTTSRef.current = false;

      // SPEED OPTIMIZATION: Create audio blob and URL in one step
      const audioBlob = new Blob([audioBuffer], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);

      // SPEED OPTIMIZATION: Set preload to auto for faster loading
      audio.preload = 'auto';

      // SPEED OPTIMIZATION: Simplified event handlers
      audio.oncanplaythrough = () => {
        updateStatus('🔊 Prehrávam odpoveď...', 'processing');
      };

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        isPlayingResponseRef.current = false;
        // Non-blocking reset
        resetDetectionStateForNewConversation().catch(() => {});
      };

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        isPlayingResponseRef.current = false;
        resetDetectionStateForNewConversation().catch(() => {});
      };

      // SPEED OPTIMIZATION: Start playing immediately
      isPlayingResponseRef.current = true;
      audio.play().catch(() => {
        isPlayingResponseRef.current = false;
        resetDetectionStateForNewConversation().catch(() => {});
      });

    } catch (error) {
      isPlayingResponseRef.current = false;
      resetDetectionStateForNewConversation().catch(() => {});
    }
  };

  // Start ping/pong keepalive
  const startPingPong = () => {
    // Send ping every 25 seconds (server pings every 30s)
    pingIntervalRef.current = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({
          type: 'ping',
          timestamp: new Date().toISOString()
        }));
        log('🏓 Ping odoslaný');
      }
    }, 25000);
  };

  // Stop ping/pong keepalive
  const stopPingPong = () => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
      log('🏓 Ping/pong zastavený');
    }
  };



  // Create new MediaRecorder for fresh WebM header
  const createNewRecorder = async () => {
    try {
      if (!streamRef.current) {
        log('❌ Žiadny stream pre nový MediaRecorder');
        return;
      }

      // Preferuj Opus - Deepgram supports it best
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        // fallback to plain WebM - NEVER use PCM!
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          throw new Error('Prehliadač nepodporuje WebM formát potrebný pre Deepgram');
        }
      }
      log(`🎤 Vytváram nový MediaRecorder s ${mimeType}`);

      mediaRecorderRef.current = new MediaRecorder(streamRef.current, {
        mimeType: mimeType
      });

      // Event handlers
      mediaRecorderRef.current.ondataavailable = (event) => {
        log(`🎵 Data available: ${event.data.size} bajtov`);
      };

      mediaRecorderRef.current.onstop = () => {
        log(`🎵 MediaRecorder zastavený`);
      };

      // Start without timeslice - we'll request data manually
      mediaRecorderRef.current.start();
      log('✅ Nový MediaRecorder spustený');

    } catch (error: any) {
      log(`❌ Chyba pri vytváraní nového MediaRecorder: ${error.message}`);
    }
  };

  // Reset detection state for new conversation - OPTIMIZED FOR SPEED
  const resetDetectionStateForNewConversation = async () => {
    // SPEED OPTIMIZATION: Reset all variables immediately without logging
    isPlayingResponseRef.current = false;
    waitingForTTSRef.current = false;

    // Clear timeouts immediately
    if (ttsTimeoutRef.current) {
      clearTimeout(ttsTimeoutRef.current);
      ttsTimeoutRef.current = null;
    }
    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }

    // Reset detection variables in batch
    consecutiveSpeechCountRef.current = 0;
    consecutiveSilenceCountRef.current = 0;
    tokenCountRef.current = 0;
    speechStartTimeRef.current = 0;
    lastTokenTimeRef.current = 0;
    isSpeakingRef.current = false;
    waitingForFirstTokenRef.current = true;

    // SPEED OPTIMIZATION: Non-blocking MediaRecorder restart
    createNewRecorder().catch(() => {});

    // SPEED OPTIMIZATION: Set status immediately
    setVoiceStatus('🎤 Pripravený na ďalšiu otázku...');
    updateStatus('🎤 Pripravený na ďalšiu otázku...', 'talking');

    // SPEED OPTIMIZATION: Restart monitoring immediately if possible
    if (isRunningRef.current && analyserRef.current) {
      // Use setTimeout to avoid blocking
      setTimeout(() => monitorAudio(), 0);
    }
  };

  // Voice Activity Detection - detect human voice
  const detectHumanVoice = (frequencyData: Uint8Array, sampleRate: number) => {
    const nyquist = sampleRate / 2;
    const binWidth = nyquist / frequencyData.length;

    const speechBands = {
      fundamental: { min: 85, max: 300 },
      formant1: { min: 300, max: 1000 },
      formant2: { min: 800, max: 2500 },
      highFreq: { min: 2500, max: 4000 }
    };

    let fundamentalEnergy = 0;
    let formant1Energy = 0;
    let formant2Energy = 0;
    let totalEnergy = 0;

    for (let i = 0; i < frequencyData.length; i++) {
      const freq = i * binWidth;
      const energy = frequencyData[i];
      totalEnergy += energy;

      if (freq >= speechBands.fundamental.min && freq <= speechBands.fundamental.max) {
        fundamentalEnergy += energy;
      } else if (freq >= speechBands.formant1.min && freq <= speechBands.formant1.max) {
        formant1Energy += energy;
      } else if (freq >= speechBands.formant2.min && freq <= speechBands.formant2.max) {
        formant2Energy += energy;
      }
    }

    if (totalEnergy === 0) return false;

    const fundamentalRatio = fundamentalEnergy / totalEnergy;
    const formant1Ratio = formant1Energy / totalEnergy;
    const formant2Ratio = formant2Energy / totalEnergy;
    const speechEnergyRatio = (fundamentalEnergy + formant1Energy + formant2Energy) / totalEnergy;

    const hasStrongFormants = formant1Ratio > 0.15 || formant2Ratio > 0.1;
    const hasSpeechSpectrum = speechEnergyRatio > 0.4;
    const hasReasonableFundamental = fundamentalRatio > 0.05 && fundamentalRatio < 0.5;

    const voiceScore = (hasStrongFormants ? 1 : 0) + (hasSpeechSpectrum ? 1 : 0) + (hasReasonableFundamental ? 1 : 0);
    return voiceScore >= 2;
  };

  // Monitor audio for voice activity detection
  const monitorAudio = () => {
    if (!isRunningRef.current || !analyserRef.current || isPlayingResponseRef.current) {
      if (isRunningRef.current && !isPlayingResponseRef.current) {
        animationFrameRef.current = requestAnimationFrame(monitorAudio);
      }
      return;
    }

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    analyserRef.current.getByteFrequencyData(dataArray);

    const rms = Math.sqrt(dataArray.reduce((sum, value) => sum + value * value, 0) / bufferLength);
    const isHumanVoice = detectHumanVoice(dataArray, analyserRef.current.context.sampleRate);

    const threshold = 50;
    const isValidSpeech = rms > threshold && isHumanVoice;

    // Update level meter
    const percentage = Math.min((rms / 50) * 100, 100);
    setAudioLevel(percentage);

    const currentTime = Date.now();

    if (isValidSpeech && !isPlayingResponseRef.current) {
      consecutiveSpeechCountRef.current++;
      consecutiveSilenceCountRef.current = 0;

      if (consecutiveSpeechCountRef.current === 1) {
        speechStartTimeRef.current = currentTime;
        lastTokenTimeRef.current = currentTime;
        if (waitingForFirstTokenRef.current) {
          tokenCountRef.current = 0;
        }
        log(`🔍 Možná reč detekovaná (RMS: ${rms.toFixed(1)}, threshold: ${threshold})`);
      }

      // Token detection
      const timeSinceLastToken = currentTime - lastTokenTimeRef.current;

      if (timeSinceLastToken > 50) {
        tokenCountRef.current++;
        lastTokenTimeRef.current = currentTime;

        if (waitingForFirstTokenRef.current && tokenCountRef.current === 1) {
          lastSpeechTimeRef.current = currentTime;
          waitingForFirstTokenRef.current = false;
          log(`🚀 PRVÝ TOKEN novej konverzácie - spúšťam 1200ms timer! (RMS: ${rms.toFixed(1)})`);
          setVoiceStatus('🗣️ Rozprávate... (1200ms timer spustený)');
        } else {
          lastSpeechTimeRef.current = currentTime;
          log(`📊 Token ${tokenCountRef.current} detekovaný (RMS: ${rms.toFixed(1)})`);
        }
      }

      if (consecutiveSpeechCountRef.current >= 8 && !isSpeakingRef.current) {
        isSpeakingRef.current = true;
        currentTranscriptRef.current = '';
        consecutiveSilenceCountRef.current = 0;
        log('🎤 Konzistentná reč detekovaná - začínam počúvať...');
        setVoiceStatus('🗣️ Rozprávate... (urobte pauzu 1200ms)');
      }

      if (isSpeakingRef.current) {
        consecutiveSilenceCountRef.current = 0;
      }

      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
      }
    } else {
      consecutiveSilenceCountRef.current++;
      consecutiveSpeechCountRef.current = 0;

      if (consecutiveSilenceCountRef.current >= 5) {
        if (consecutiveSpeechCountRef.current > 0 && consecutiveSpeechCountRef.current < 3) {
          const timeSinceStart = currentTime - speechStartTimeRef.current;
          log(`🔇 Krátky šum ignorovaný (${consecutiveSpeechCountRef.current} detekcií, ${tokenCountRef.current} tokenov, ${timeSinceStart}ms)`);

          consecutiveSpeechCountRef.current = 0;
          tokenCountRef.current = 0;
          speechStartTimeRef.current = 0;
          lastTokenTimeRef.current = 0;
        }
      }

      // 1200ms timeout for better end-of-speech detection
      const timeSinceLastToken = currentTime - lastTokenTimeRef.current;
      const hasTokens = tokenCountRef.current > 0;
      const tokenTimeout = 1200;

      if (hasTokens && timeSinceLastToken > tokenTimeout && !isPlayingResponseRef.current) {
        log(`⏰ ${tokenTimeout}ms timeout od posledného tokenu - spracovávam audio`);
        log(`📊 Tokeny: ${tokenCountRef.current}, Čas od posledného tokenu: ${timeSinceLastToken}ms, Timeout: ${tokenTimeout}ms`);

        const speechDuration = currentTime - speechStartTimeRef.current;
        log(`🔍 Debug: speechDuration=${speechDuration}ms`);
        log(`📡 Spracovávam audio...`);
        processStreamAudio();
        return;
      }
    }

    if (isRunningRef.current) {
      animationFrameRef.current = requestAnimationFrame(monitorAudio);
    }
  };

  const startVoiceChat = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      addSystemMessage('❌ Váš prehliadač nepodporuje mikrofón. Skúste Chrome, Firefox alebo Safari.');
      return;
    }

    try {
      log('🚀 Spúšťam voice chat s Voice Activity Detection...');
      updateStatus('Žiadam o prístup k mikrofónu...', 'connecting');

      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000
        }
      });

      // Store stream for possible MediaRecorder restart
      streamRef.current = stream;
      log('✅ Mikrofón prístup udelený');

      // Initialize Web Audio API
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);

      analyserRef.current.smoothingTimeConstant = 0.3;
      analyserRef.current.fftSize = 512;
      microphoneRef.current.connect(analyserRef.current);

      log('✅ Web Audio API inicializované');

      // Setup MediaRecorder for audio capture with Deepgram-compatible format
      // Prefer Opus - Deepgram supports it best
      let mimeType = 'audio/webm;codecs=opus';
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        // fallback to plain WebM - NEVER use PCM!
        mimeType = 'audio/webm';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          throw new Error('Prehliadač nepodporuje WebM formát potrebný pre Deepgram');
        }
      }
      log(`🎤 Používam ${mimeType} pre Deepgram`);

      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: mimeType
      });

      // We no longer need to collect chunks automatically - we only use requestData()
      mediaRecorderRef.current.ondataavailable = (event) => {
        // Event handler is only used for requestData() calls
        log(`🎵 Data available: ${event.data.size} bajtov`);
      };

      mediaRecorderRef.current.onstop = () => {
        log(`🎵 MediaRecorder zastavený`);
      };

      // start once, without timeslice, we'll request data manually
      mediaRecorderRef.current.start();
      log('✅ MediaRecorder spustený');

      // Start voice activity detection
      isRunningRef.current = true;
      waitingForFirstTokenRef.current = true;
      setIsRecording(true);

      log('✅ Mikrofón pripojený, začínam Voice Activity Detection...');
      updateStatus('🎤 Počúvam... hovorte', 'talking');

      monitorAudio();

    } catch (error: any) {
      log(`❌ Chyba pri spustení voice chat: ${error.message}`);
      addSystemMessage(`❌ Nepodarilo sa spustiť voice chat: ${error.message}`);
      updateStatus('❌ Chyba voice chat', 'ready');

      if (error.name === 'NotAllowedError') {
        addSystemMessage('💡 Tip: Kliknite na ikonu zámku v adresnom riadku a povoľte mikrofón.');
      }
    }
  };

  const stopVoiceChat = () => {
    log('⏹️ Zastavujem voice chat...');

    isRunningRef.current = false;

    if (silenceTimerRef.current) {
      clearTimeout(silenceTimerRef.current);
      silenceTimerRef.current = null;
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      try {
        mediaRecorderRef.current.stop();
      } catch (error: any) {
        log(`❌ Chyba pri zastavení MediaRecorder: ${error.message}`);
      }
      mediaRecorderRef.current = null;
    }

    if (audioContextRef.current) {
      try {
        audioContextRef.current.close();
      } catch (error: any) {
        log(`❌ Chyba pri zatvorení AudioContext: ${error.message}`);
      }
      audioContextRef.current = null;
    }

    // Clean up stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    audioChunksRef.current = [];
    setAudioLevel(0);
    setIsRecording(false);

    log('✅ Voice chat zastavený');
    updateStatus('Voice chat zastavený', 'ready');
  };

  // Process stream audio - OPTIMIZED FOR SPEED
  const processStreamAudio = async () => {
    try {
      // Protection against duplicate processing
      if (isProcessingAudioRef.current) {
        return;
      }

      if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
        setVoiceStatus('❌ WebSocket nie je pripojený');
        return;
      }

      // Set processing flag immediately
      isProcessingAudioRef.current = true;
      setVoiceStatus('📡 Posielam audio...');

      // SPEED OPTIMIZATION: Use immediate Promise resolution with timeout fallback
      const webmBlob = await Promise.race([
        new Promise<Blob>(resolve => {
          const ondata = (e: BlobEvent) => {
            mediaRecorderRef.current?.removeEventListener('dataavailable', ondata);
            resolve(e.data);
          };
          mediaRecorderRef.current?.addEventListener('dataavailable', ondata);
          mediaRecorderRef.current?.requestData();
        }),
        // Fallback timeout after 100ms to prevent hanging
        new Promise<Blob>((_, reject) =>
          setTimeout(() => reject(new Error('MediaRecorder timeout')), 100)
        )
      ]);

      // SPEED OPTIMIZATION: Parallel conversion and send
      const buffer = await webmBlob.arrayBuffer();

      // Send immediately without waiting
      wsRef.current.send(buffer);
      setVoiceStatus('🔄 Spracovávam...');

      // Set flag to prevent new detection until response is complete
      isPlayingResponseRef.current = true;

      // Reset processing flag immediately
      isProcessingAudioRef.current = false;

    } catch (error: any) {
      setVoiceStatus('❌ Chyba pri spracovaní audio');
      isProcessingAudioRef.current = false;
      // Non-blocking error handling
      resetDetectionStateForNewConversation().catch(() => {});
    }
  };

  const endDemo = () => {
    log('🔚 Ukončujem demo...');

    // Stop voice chat if running
    if (isRecording) {
      stopVoiceChat();
    }

    // Stop ping/pong
    stopPingPong();

    // Close WebSocket
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    // Clear all timeouts
    if (ttsTimeoutRef.current) {
      clearTimeout(ttsTimeoutRef.current);
      ttsTimeoutRef.current = null;
    }

    // Reset state
    setIsRecording(false);
    setIsConnected(false);
    setDemoStatus('idle');
    setMessages([]);
    setSessionId(null);
    setAudioLevel(0);
    setVoiceStatus('Pripravený na rozpoznávanie hlasu...');

    log('✅ Demo ukončené');
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      endDemo();
    };
  }, []);

  // Determine if we should show split layout (after first response is generated)
  const hasGeneratedResponse = messages.some(msg => msg.role === 'assistant');

  return (
    <div className={`bg-card rounded-2xl border shadow-xl p-8 md:p-12 ${className}`}>
      {/* Split Layout - shown after first response */}
      {hasGeneratedResponse ? (
        <div className="flex flex-col lg:flex-row gap-8 h-full">
          {/* Left Side - Voice Controls */}
          <div className="lg:w-1/2 flex flex-col">
            <div className="text-center">
              <div className="relative inline-block">
                {/* Main Voice Button */}
                <div className={`
                  w-24 h-24 md:w-32 md:h-32 rounded-full flex items-center justify-center transition-all duration-300 relative cursor-pointer
                  ${demoStatus === 'idle' ? 'bg-muted/50 hover:bg-muted/70' : ''}
                  ${demoStatus === 'connecting' ? 'bg-primary/20 animate-pulse' : ''}
                  ${demoStatus === 'ready' ? 'bg-primary/10 hover:bg-primary/20' : ''}
                  ${demoStatus === 'talking' ? 'bg-primary/30 animate-pulse' : ''}
                  ${demoStatus === 'processing' ? 'bg-accent/30 animate-pulse' : ''}
                `} onClick={demoStatus === 'idle' ? startDemo : demoStatus === 'ready' ? toggleRecording : demoStatus === 'talking' ? toggleRecording : undefined}>
                  {demoStatus === 'idle' && (
                    <Phone className="w-8 h-8 md:w-12 md:h-12 text-muted-foreground" />
                  )}
                  {demoStatus === 'connecting' && (
                    <Zap className="w-8 h-8 md:w-12 md:h-12 text-primary animate-spin" />
                  )}
                  {demoStatus === 'ready' && !isRecording && (
                    <Mic className="w-8 h-8 md:w-12 md:h-12 text-primary" />
                  )}
                  {demoStatus === 'talking' && isRecording && (
                    <Volume2 className="w-8 h-8 md:w-12 md:h-12 text-primary animate-bounce" />
                  )}
                  {demoStatus === 'processing' && (
                    <MessageCircle className="w-8 h-8 md:w-12 md:h-12 text-accent animate-spin" />
                  )}
                </div>

                {/* Pulsing rings for active states */}
                {(demoStatus === 'talking' || demoStatus === 'connecting' || demoStatus === 'processing') && (
                  <>
                    <div className="absolute inset-0 rounded-full border-2 border-primary animate-ping opacity-60" />
                    <div className="absolute inset-0 rounded-full border border-primary animate-ping opacity-40" style={{ animationDelay: '0.5s' }} />
                  </>
                )}
              </div>

              {/* Audio Level Meter */}
              {isRecording && (
                <div className="mt-4 w-48 mx-auto">
                  <div className="w-full h-3 bg-muted rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 transition-all duration-100"
                      style={{ width: `${Math.min(audioLevel, 100)}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Status Text */}
              <div className="mt-4 mb-6">
                <p className="text-base font-medium">{voiceStatus}</p>
                {sessionId && (
                  <p className="text-xs text-muted-foreground mt-1">Session: {sessionId.split('_')[1]}</p>
                )}
              </div>

              {/* Control Buttons */}
              <div className="flex flex-col gap-3">
                {demoStatus === 'idle' && (
                  <Button onClick={startDemo} size="default" className="w-full">
                    <Play className="w-4 h-4 mr-2" />
                    Start Demo
                  </Button>
                )}

                {demoStatus === 'ready' && (
                  <Button onClick={toggleRecording} size="default" className="w-full">
                    <Mic className="w-4 h-4 mr-2" />
                    Start Speaking
                  </Button>
                )}

                {demoStatus === 'talking' && (
                  <Button onClick={toggleRecording} variant="secondary" size="default" className="w-full">
                    <Square className="w-4 h-4 mr-2" />
                    Stop Speaking
                  </Button>
                )}

                {isConnected && (
                  <Button onClick={endDemo} variant="outline" size="default" className="w-full">
                    <Phone className="w-4 h-4 mr-2" />
                    End Demo
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Right Side - Chat Messages */}
          <div className="lg:w-1/2 flex flex-col h-full">
            <div className="flex-1 overflow-y-auto space-y-3 bg-muted/20 rounded-lg p-4" style={{ maxHeight: 'calc(100vh - 300px)' }}>
              {messages.map((message, index) => (
                <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`
                    max-w-[85%] p-3 rounded-lg text-sm
                    ${message.role === 'user' ? 'bg-primary text-primary-foreground' : ''}
                    ${message.role === 'assistant' ? 'bg-card border' : ''}
                    ${message.role === 'system' ? 'bg-accent/20 text-accent-foreground border border-accent/30 italic' : ''}
                  `}>
                    {message.content}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        /* Centered Layout - shown before first response */
        <div className="text-center">
          <div className="relative inline-block">
            {/* Main Voice Button */}
            <div className={`
              w-32 h-32 md:w-40 md:h-40 rounded-full flex items-center justify-center transition-all duration-300 relative cursor-pointer
              ${demoStatus === 'idle' ? 'bg-muted/50 hover:bg-muted/70' : ''}
              ${demoStatus === 'connecting' ? 'bg-primary/20 animate-pulse' : ''}
              ${demoStatus === 'ready' ? 'bg-primary/10 hover:bg-primary/20' : ''}
              ${demoStatus === 'talking' ? 'bg-primary/30 animate-pulse' : ''}
              ${demoStatus === 'processing' ? 'bg-accent/30 animate-pulse' : ''}
            `} onClick={demoStatus === 'idle' ? startDemo : demoStatus === 'ready' ? toggleRecording : demoStatus === 'talking' ? toggleRecording : undefined}>
              {demoStatus === 'idle' && (
                <Phone className="w-12 h-12 md:w-16 md:h-16 text-muted-foreground" />
              )}
              {demoStatus === 'connecting' && (
                <Zap className="w-12 h-12 md:w-16 md:h-16 text-primary animate-spin" />
              )}
              {demoStatus === 'ready' && !isRecording && (
                <Mic className="w-12 h-12 md:w-16 md:h-16 text-primary" />
              )}
              {demoStatus === 'talking' && isRecording && (
                <Volume2 className="w-12 h-12 md:w-16 md:h-16 text-primary animate-bounce" />
              )}
              {demoStatus === 'processing' && (
                <MessageCircle className="w-12 h-12 md:w-16 md:h-16 text-accent animate-spin" />
              )}
            </div>

            {/* Pulsing rings for active states */}
            {(demoStatus === 'talking' || demoStatus === 'connecting' || demoStatus === 'processing') && (
              <>
                <div className="absolute inset-0 rounded-full border-2 border-primary animate-ping opacity-60" />
                <div className="absolute inset-0 rounded-full border border-primary animate-ping opacity-40" style={{ animationDelay: '0.5s' }} />
              </>
            )}
          </div>

          {/* Audio Level Meter */}
          {isRecording && (
            <div className="mt-6 w-64 mx-auto">
              <div className="w-full h-4 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 transition-all duration-100"
                  style={{ width: `${Math.min(audioLevel, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* Status Text */}
          <div className="mt-6 mb-8">
            <p className="text-lg font-medium">{voiceStatus}</p>
            {sessionId && (
              <p className="text-sm text-muted-foreground mt-2">Session: {sessionId.split('_')[1]}</p>
            )}
          </div>

          {/* Control Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {demoStatus === 'idle' && (
              <Button onClick={startDemo} size="lg" className="min-w-[160px]">
                <Play className="w-5 h-5 mr-2" />
                Start Demo
              </Button>
            )}

            {demoStatus === 'ready' && (
              <Button onClick={toggleRecording} size="lg" className="min-w-[160px]">
                <Mic className="w-5 h-5 mr-2" />
                Start Speaking
              </Button>
            )}

            {demoStatus === 'talking' && (
              <Button onClick={toggleRecording} variant="secondary" size="lg" className="min-w-[160px]">
                <Square className="w-5 h-5 mr-2" />
                Stop Speaking
              </Button>
            )}

            {isConnected && (
              <Button onClick={endDemo} variant="outline" size="lg" className="min-w-[160px]">
                <Phone className="w-5 h-5 mr-2" />
                End Demo
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
