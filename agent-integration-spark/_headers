/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(self), geolocation=(), payment=()

/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With
  Access-Control-Max-Age: 86400

/*.js
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

/*.mjs
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

/*.ts
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

/*.tsx
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

/*.css
  Content-Type: text/css
  Cache-Control: public, max-age=31536000, immutable

/*.html
  Cache-Control: public, max-age=300
