#!/bin/bash

# Fix MIME types for JavaScript modules in both frontends

set -e

echo "🔧 Fixing MIME types for JavaScript modules..."

# Fix agent-integration-spark
echo "📦 Building agent-integration-spark..."
cd ../agent-integration-spark

# Clean and build
rm -rf dist
npm run build

# Ensure _headers file is in dist
cp _headers dist/_headers

echo "✅ agent-integration-spark build complete with MIME type fixes"

# Go back to root
cd ..

# Fix cloudflare-clean
echo "📦 Updating cloudflare-clean..."
cd cloudflare-clean

# Run build script if it exists
if [ -f "build.js" ]; then
    node build.js
fi

echo "✅ cloudflare-clean updated with MIME type fixes"

cd ..

echo "🎉 All MIME type fixes applied!"
echo ""
echo "📋 Changes made:"
echo "  ✅ Updated _headers files with correct Content-Type for .js, .mjs, .ts, .tsx"
echo "  ✅ Updated wrangler.toml files with header configurations"
echo "  ✅ Updated Vite config with proper build settings"
echo "  ✅ Rebuilt agent-integration-spark with new settings"
echo ""
echo "🚀 Ready to deploy to Cloudflare Pages!"
