#!/bin/bash

# Check Oracle server status and PM2 processes
set -e

# Configuration
ORACLE_HOST="*************"
SSH_KEY="/Users/<USER>/Documents/augment-projects/chatko/ssh-key-2025-07-16 (3).key"
SSH_USER="ubuntu"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔍 Checking Oracle Server Status${NC}"
echo "=================================="

# Check SSH connection
echo "Testing SSH connection..."
if ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SSH_USER@$ORACLE_HOST" "echo 'SSH OK'" >/dev/null 2>&1; then
    echo -e "${GREEN}✅ SSH connection successful${NC}"
else
    echo -e "${YELLOW}❌ SSH connection failed${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}📊 PM2 Status:${NC}"
ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "pm2 status"

echo ""
echo -e "${BLUE}📁 Directory Structure:${NC}"
ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "
echo 'Current directories in /home/<USER>'
ls -la /home/<USER>/ | grep -E '^d.*chat|^d.*voice|^d.*server'
echo ''
echo 'PM2 processes:'
pm2 jlist | jq -r '.[] | \"Name: \" + .name + \", Status: \" + .pm2_env.status + \", CWD: \" + .pm2_env.pm_cwd' 2>/dev/null || echo 'jq not available'
"

echo ""
echo -e "${BLUE}🌐 Service Tests:${NC}"
echo "Testing health endpoint..."
if curl -f -s "http://$ORACLE_HOST:3000/health" >/dev/null; then
    echo -e "${GREEN}✅ Health check passed (port 3000)${NC}"
else
    echo -e "${YELLOW}❌ Health check failed (port 3000)${NC}"
fi

if curl -f -s "http://$ORACLE_HOST:80/health" >/dev/null; then
    echo -e "${GREEN}✅ Health check passed (port 80)${NC}"
else
    echo -e "${YELLOW}❌ Health check failed (port 80)${NC}"
fi

echo ""
echo -e "${BLUE}📋 Recent PM2 Logs:${NC}"
ssh -i "$SSH_KEY" "$SSH_USER@$ORACLE_HOST" "pm2 logs --lines 5"
